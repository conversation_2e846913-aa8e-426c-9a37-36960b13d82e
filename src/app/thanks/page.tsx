'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Navigation from '@/components/Navigation';
import {
  Heart,
  Plus,
  X,
  Send,
  Sparkles,
  Users,
  MessageCircle,
  Calendar,
  Award,
  Smile,
  ThumbsUp,
  Star,
  Zap,
  User
} from 'lucide-react';

interface Employee {
  _id: string;
  name: string;
  email: string;
  image?: string;
  department?: string;
  position?: string;
}

interface Thanks {
  _id: string;
  fromEmployee?: Employee;
  toEmployee: Employee;
  anonymousSender?: {
    name: string;
  };
  message: string;
  category: string;
  isPublic: boolean;
  aiGenerated: boolean;
  createdAt: string;
  reactions: {
    emoji: string;
    employees: string[];
  }[];
}

function ThanksPageContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [thanks, setThanks] = useState<Thanks[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showReactionPicker, setShowReactionPicker] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<Employee | null>(null);
  const [preSelectedEmployee, setPreSelectedEmployee] = useState<string | null>(null);
  const [employeeSearch, setEmployeeSearch] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [messageLength, setMessageLength] = useState(0);
  const [anonymousName, setAnonymousName] = useState('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'teamwork': return <Users className="w-4 h-4" />;
      case 'leadership': return <Award className="w-4 h-4" />;
      case 'innovation': return <Zap className="w-4 h-4" />;
      case 'helpfulness': return <Heart className="w-4 h-4" />;
      case 'quality': return <Star className="w-4 h-4" />;
      default: return <ThumbsUp className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'teamwork': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'leadership': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'innovation': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'helpfulness': return 'bg-pink-100 text-pink-700 border-pink-200';
      case 'quality': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const reactionEmojis = ['👍', '❤️', '😊', '🎉', '👏', '🔥', '💯', '🚀'];

  const handleReaction = async (thanksId: string, emoji: string) => {
    try {
      const response = await fetch(`/api/thanks/${thanksId}/reactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ emoji }),
      });

      if (response.ok) {
        const data = await response.json();
        // Update the thanks list with new reactions
        setThanks(prevThanks =>
          prevThanks.map(thank =>
            thank._id === thanksId
              ? { ...thank, reactions: data.reactions }
              : thank
          )
        );
      }
    } catch (error) {
      console.error('Error handling reaction:', error);
    }
    setShowReactionPicker(null);
  };

  const getUserReaction = (reactions: any[], userId: string) => {
    for (const reaction of reactions) {
      if (reaction.employees.some((emp: any) => emp._id === userId)) {
        return reaction.emoji;
      }
    }
    return null;
  };

  const getReactionCount = (reactions: any[], emoji: string) => {
    const reaction = reactions.find(r => r.emoji === emoji);
    return reaction ? reaction.employees.length : 0;
  };

  useEffect(() => {
    // Allow both authenticated and unauthenticated users
    fetchThanks();
    fetchEmployees();
    if (status === 'authenticated') {
      fetchCurrentUser();
    }
  }, [status]);

  // Handle URL parameters for pre-selecting employee
  useEffect(() => {
    const toEmployeeId = searchParams.get('to');
    if (toEmployeeId && employees.length > 0) {
      const employee = employees.find(emp => emp._id === toEmployeeId);
      if (employee) {
        setSelectedEmployee(employee);
        setEmployeeSearch(employee.name);
        setPreSelectedEmployee(toEmployeeId);
        setShowForm(true); // Auto-open form when coming from signature link
      }
    }
  }, [searchParams, employees]);

  // Handle employee search and filtering
  useEffect(() => {
    if (employeeSearch.trim() === '') {
      setFilteredEmployees([]);
      setShowSuggestions(false);
      return;
    }

    const filtered = employees
      .filter(emp => session?.user?.email ? emp.email !== session.user.email : true) // Exclude current user only if authenticated
      .filter(emp =>
        emp.name.toLowerCase().includes(employeeSearch.toLowerCase()) ||
        emp.email.toLowerCase().includes(employeeSearch.toLowerCase()) ||
        (emp.department && emp.department.toLowerCase().includes(employeeSearch.toLowerCase()))
      )
      .slice(0, 5); // Limit to 5 suggestions

    setFilteredEmployees(filtered);
    setShowSuggestions(filtered.length > 0 && employeeSearch.length > 0);
  }, [employeeSearch, employees, session?.user?.email]);

  const handleEmployeeSelect = (employee: Employee) => {
    setSelectedEmployee(employee);
    setEmployeeSearch(employee.name);
    setShowSuggestions(false);
    setPreSelectedEmployee(employee._id);
  };

  const handleEmployeeSearchChange = (value: string) => {
    setEmployeeSearch(value);
    if (value.trim() === '') {
      setSelectedEmployee(null);
      setPreSelectedEmployee(null);
    }
  };

  // Close reaction picker and suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showReactionPicker) {
        setShowReactionPicker(null);
      }
      if (showSuggestions) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showReactionPicker, showSuggestions]);

  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/employees/me');
      if (response.ok) {
        const data = await response.json();
        setCurrentUser(data);
      }
    } catch (error) {
      console.error('Error fetching current user:', error);
    }
  };

  const fetchThanks = async () => {
    try {
      const response = await fetch('/api/thanks');
      if (response.ok) {
        const data = await response.json();
        // The API returns { thanks: [...], pagination: {...} }
        setThanks(data.thanks || []);
      } else {
        console.error('Failed to fetch thanks:', response.status);
        setThanks([]);
      }
    } catch (error) {
      console.error('Error fetching thanks:', error);
      setThanks([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees');
      if (response.ok) {
        const data = await response.json();
        // The API returns { employees: [...], pagination: {...} }
        setEmployees(data.employees || []);
      } else {
        console.error('Failed to fetch employees:', response.status);
        setEmployees([]);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
      setEmployees([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const thanksData = {
      toEmployee: selectedEmployee?._id || preSelectedEmployee,
      message: formData.get('message'),
      category: formData.get('category'),
      isPublic: formData.get('isPublic') === 'on',
      // Add anonymous sender info if not authenticated
      ...(status !== 'authenticated' && {
        anonymousSender: {
          name: anonymousName.trim() || 'Anonymous'
        }
      })
    };

    // Validate required fields
    if (!thanksData.toEmployee || !thanksData.message || !thanksData.category) {
      alert('Please fill in all required fields');
      setSubmitting(false);
      return;
    }

    // Validate anonymous name if not authenticated
    if (status !== 'authenticated' && !anonymousName.trim()) {
      alert('Please enter your name');
      setSubmitting(false);
      return;
    }

    // Validate message length
    if (typeof thanksData.message === 'string' && thanksData.message.trim().length < 10) {
      alert('Please write a message with at least 10 characters');
      setSubmitting(false);
      return;
    }

    // Validate that employee search matches a selected employee
    if (!selectedEmployee && employeeSearch.trim() !== '') {
      alert('Please select a valid employee from the suggestions');
      setSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/thanks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(thanksData),
      });

      if (response.ok) {
        setShowForm(false);
        setPreSelectedEmployee(null); // Clear pre-selection
        setSelectedEmployee(null); // Clear selected employee
        setEmployeeSearch(''); // Clear search
        setShowSuggestions(false); // Hide suggestions
        setMessageLength(0); // Reset message length
        setAnonymousName(''); // Clear anonymous name
        fetchThanks();
        (e.target as HTMLFormElement).reset();
        alert('Thanks sent successfully!');
      } else {
        const errorData = await response.json();
        alert(`Failed to send thanks: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error sending thanks:', error);
      alert('Error sending thanks. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const generateAIMessage = async (category: string, toEmployeeId: string) => {
    try {
      const response = await fetch('/api/ai/generate-thanks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ category, toEmployeeId }),
      });

      if (response.ok) {
        const data = await response.json();
        const messageTextarea = document.querySelector('textarea[name="message"]') as HTMLTextAreaElement;
        if (messageTextarea) {
          messageTextarea.value = data.message;
          setMessageLength(data.message.length); // Update character count

          // Add a visual indicator if it's a fallback message
          if (data.fallback) {
            messageTextarea.placeholder = 'AI-suggested message (using fallback template)';
          } else {
            messageTextarea.placeholder = 'AI-suggested message';
          }

          // Trigger change event to update any other listeners
          messageTextarea.dispatchEvent(new Event('change', { bubbles: true }));
        }
      } else {
        console.warn('AI message generation failed, user can write manually');
      }
    } catch (error) {
      console.error('Error generating AI message:', error);
      // Silently fail - user can still write their own message
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
        <Navigation />
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="text-center mb-8">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-pink-200 border-t-pink-600 rounded-full mx-auto mb-4"
            />
            <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
              Loading Thanks...
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
                className="bg-white shadow-lg rounded-2xl p-6 border border-gray-100"
              >
                <div className="animate-pulse">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Allow both authenticated and unauthenticated users to access the page

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <Navigation />
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-3 mb-4">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="p-3 bg-gradient-to-r from-pink-500 to-purple-600 rounded-2xl shadow-lg"
            >
              <Heart className="w-8 h-8 text-white" />
            </motion.div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
              Thanks Board
            </h1>
          </div>
          <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
            Spread appreciation and recognize great work. Share thanks with your colleagues and build a positive workplace culture.
          </p>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowForm(!showForm)}
            className={`inline-flex items-center space-x-2 px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg transition-all duration-200 ${
              showForm
                ? 'bg-gray-600 hover:bg-gray-700 text-white'
                : 'bg-gradient-to-r from-pink-600 to-purple-600 hover:shadow-xl text-white'
            }`}
          >
            {showForm ? (
              <>
                <X className="w-5 h-5" />
                <span>Cancel</span>
              </>
            ) : (
              <>
                <Plus className="w-5 h-5" />
                <span>Send Thanks</span>
              </>
            )}
          </motion.button>
        </motion.div>

        {/* Thanks Form */}
        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3 }}
              className="bg-white shadow-2xl rounded-3xl p-8 mb-12 border border-gray-100"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-pink-500 to-purple-600 rounded-xl">
                    <Send className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Send Thanks</h2>
                </div>
                {preSelectedEmployee && (
                  <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                    <Heart className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-700">
                      Ready to send thanks!
                    </span>
                  </div>
                )}
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="relative">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    <Users className="w-4 h-4 inline mr-2" />
                    To Employee
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={employeeSearch}
                      onChange={(e) => handleEmployeeSearchChange(e.target.value)}
                      onFocus={() => setShowSuggestions(filteredEmployees.length > 0)}
                      placeholder="Type employee name to search..."
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white text-gray-900"
                      autoComplete="off"
                    />
                    {selectedEmployee && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="flex items-center space-x-2">
                          <span className="text-green-600 text-sm">✓</span>
                          <button
                            type="button"
                            onClick={() => {
                              setSelectedEmployee(null);
                              setEmployeeSearch('');
                              setPreSelectedEmployee(null);
                            }}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            ✕
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Autocomplete Suggestions */}
                  <AnimatePresence>
                    {showSuggestions && filteredEmployees.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto"
                      >
                        {filteredEmployees.map((employee) => (
                          <motion.button
                            key={employee._id}
                            type="button"
                            whileHover={{ backgroundColor: '#f9fafb' }}
                            onClick={() => handleEmployeeSelect(employee)}
                            className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex items-center space-x-3">
                              {employee.image ? (
                                <img
                                  src={employee.image}
                                  alt={employee.name}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                  {employee.name.charAt(0)}
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {employee.name}
                                </p>
                                <p className="text-xs text-gray-500 truncate">
                                  {employee.department || 'No department'} • {employee.email}
                                </p>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* No results message */}
                  {employeeSearch.length > 0 && filteredEmployees.length === 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg p-4">
                      <p className="text-sm text-gray-500 text-center">
                        No employees found matching "{employeeSearch}"
                      </p>
                    </div>
                  )}
                </div>

                {/* Anonymous Name Field - Only show for non-authenticated users */}
                {status !== 'authenticated' && (
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      <User className="w-4 h-4 inline mr-2" />
                      Your Name
                    </label>
                    <input
                      type="text"
                      value={anonymousName}
                      onChange={(e) => setAnonymousName(e.target.value)}
                      placeholder="Enter your name..."
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white text-gray-900"
                      required
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    <Award className="w-4 h-4 inline mr-2" />
                    Category
                  </label>
                  <select
                    name="category"
                    required
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    onChange={(e) => {
                      if (e.target.value && selectedEmployee?._id) {
                        generateAIMessage(e.target.value, selectedEmployee._id);
                      }
                    }}
                  >
                    <option value="">Select a category</option>
                    <option value="teamwork">🤝 Teamwork</option>
                    <option value="leadership">👑 Leadership</option>
                    <option value="innovation">⚡ Innovation</option>
                    <option value="helpfulness">💝 Helpfulness</option>
                    <option value="quality">⭐ Quality Work</option>
                    <option value="other">🎯 Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    <MessageCircle className="w-4 h-4 inline mr-2" />
                    Message
                  </label>
                  <div className="relative">
                    <textarea
                      name="message"
                      rows={4}
                      required
                      onChange={(e) => setMessageLength(e.target.value.length)}
                      className={`w-full px-4 py-3 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white resize-none text-gray-900 ${
                        messageLength > 0 && messageLength < 10
                          ? 'border-red-300 focus:ring-red-500'
                          : messageLength >= 10
                          ? 'border-green-300 focus:ring-green-500'
                          : 'border-gray-200'
                      }`}
                      placeholder="Write your heartfelt thanks message... (minimum 10 characters)"
                    />
                    <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                      <span className={messageLength < 10 ? 'text-red-500' : 'text-green-500'}>
                        {messageLength}/10 min
                      </span>
                    </div>
                  </div>
                  <div className="mt-2 flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Sparkles className="w-4 h-4" />
                      <span>Tip: Select a category and employee to generate an AI-suggested message</span>
                    </div>
                    {messageLength > 0 && messageLength < 10 && (
                      <div className="text-xs text-red-500">
                        Need {10 - messageLength} more characters
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl border border-pink-100">
                  <input
                    type="checkbox"
                    name="isPublic"
                    id="isPublic"
                    defaultChecked
                    className="h-5 w-5 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isPublic" className="flex items-center space-x-2 text-sm font-medium text-gray-900">
                    <Users className="w-4 h-4" />
                    <span>Make this thanks public</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-6 py-3 border-2 border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="submit"
                    disabled={submitting}
                    className="px-8 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl transition-all duration-200 disabled:opacity-50 font-semibold shadow-lg hover:shadow-xl flex items-center space-x-2"
                  >
                    {submitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        />
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        <span>Send Thanks</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Thanks Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {!Array.isArray(thanks) || thanks.length === 0 ? (
            <motion.div
              variants={itemVariants}
              className="col-span-full bg-white shadow-lg rounded-3xl p-12 text-center border border-gray-100"
            >
              <div className="text-6xl mb-4">💝</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">No Thanks Yet</h3>
              <p className="text-gray-600 text-lg">
                Be the first to spread some appreciation! Click "Send Thanks" to get started.
              </p>
            </motion.div>
          ) : (
            thanks.map((thank, index) => (
              <motion.div
                key={thank._id}
                variants={itemVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                className="bg-white shadow-lg rounded-3xl p-6 border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {thank.fromEmployee && thank.fromEmployee.image ? (
                      <motion.img
                        whileHover={{ scale: 1.1 }}
                        src={thank.fromEmployee.image}
                        alt={thank.fromEmployee.name}
                        className="h-14 w-14 rounded-2xl shadow-md object-cover"
                      />
                    ) : (
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        className="h-14 w-14 rounded-2xl bg-gradient-to-r from-primary-400 to-primary-600 flex items-center justify-center shadow-md"
                      >
                        <span className="text-white font-bold text-lg">
                          {thank.fromEmployee?.name?.charAt(0) || thank.anonymousSender?.name?.charAt(0) || '?'}
                        </span>
                      </motion.div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      <span className="font-bold text-gray-900 text-lg">
                        {thank.fromEmployee?.name || thank.anonymousSender?.name || 'Anonymous'}
                      </span>
                      {thank.anonymousSender && (
                        <span className="inline-flex items-center space-x-1 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium">
                          <User className="w-3 h-3" />
                          <span>Guest</span>
                        </span>
                      )}
                      <Heart className="w-4 h-4 text-pink-500" />
                      <span className="font-bold text-gray-900 text-lg">
                        {thank.toEmployee?.name || 'Unknown Employee'}
                      </span>
                    </div>

                    <div className="flex flex-wrap items-center gap-2 mb-4">
                      <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(thank.category)}`}>
                        {getCategoryIcon(thank.category)}
                        <span className="capitalize">{thank.category}</span>
                      </span>
                      {thank.aiGenerated && (
                        <span className="inline-flex items-center space-x-1 bg-gradient-to-r from-primary-500 to-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                          <Sparkles className="w-3 h-3" />
                          <span>AI-assisted</span>
                        </span>
                      )}
                    </div>

                    <p className="text-gray-700 mb-4 leading-relaxed text-lg">
                      "{thank.message}"
                    </p>

                    {/* Reactions Section */}
                    <div className="pt-4 border-t border-gray-100">
                      {/* Existing Reactions */}
                      {thank.reactions && thank.reactions.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {thank.reactions.map((reaction, reactionIndex) => (
                            <motion.button
                              key={`${reaction.emoji}-${reactionIndex}`}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => currentUser && handleReaction(thank._id, reaction.emoji)}
                              className={`flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 min-w-max ${
                                currentUser && reaction.employees.some((emp: any) => emp._id === currentUser._id)
                                  ? 'bg-pink-100 border-2 border-pink-300 text-pink-700 shadow-md'
                                  : 'bg-gray-100 border-2 border-gray-200 text-gray-600 hover:bg-gray-200 hover:border-gray-300'
                              }`}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <span className="text-lg leading-none">{reaction.emoji}</span>
                              <span className="font-semibold leading-none">{reaction.employees.length}</span>
                            </motion.button>
                          ))}
                        </div>
                      )}

                      {/* Bottom Row */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-gray-500">
                          <Calendar className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {new Date(thank.createdAt).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </span>
                        </div>

                        {/* Reaction Picker */}
                        <div className="relative">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setShowReactionPicker(
                              showReactionPicker === thank._id ? null : thank._id
                            )}
                            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-all duration-200 text-gray-600 hover:text-gray-700 border-2 border-transparent hover:border-gray-300 font-medium"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              minWidth: 'max-content'
                            }}
                          >
                            <Smile className="w-4 h-4" />
                            <span className="text-sm font-medium leading-none">React</span>
                          </motion.button>

                          {/* Reaction Picker Dropdown */}
                          <AnimatePresence>
                            {showReactionPicker === thank._id && (
                              <motion.div
                                initial={{ opacity: 0, scale: 0.8, y: 10 }}
                                animate={{ opacity: 1, scale: 1, y: 0 }}
                                exit={{ opacity: 0, scale: 0.8, y: 10 }}
                                transition={{ duration: 0.2 }}
                                className="absolute bottom-full right-0 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 z-50 min-w-max"
                                style={{
                                  transformOrigin: 'bottom right',
                                  minWidth: '200px'
                                }}
                              >
                                <div className="grid grid-cols-4 gap-3">
                                  {reactionEmojis.map((emoji) => (
                                    <motion.button
                                      key={emoji}
                                      whileHover={{ scale: 1.15 }}
                                      whileTap={{ scale: 0.9 }}
                                      onClick={() => currentUser && handleReaction(thank._id, emoji)}
                                      className="w-12 h-12 flex items-center justify-center text-2xl hover:bg-gray-100 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-gray-300"
                                      style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                      }}
                                    >
                                      {emoji}
                                    </motion.button>
                                  ))}
                                </div>
                                {/* Arrow pointer */}
                                <div className="absolute top-full right-4 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white"></div>
                                <div className="absolute top-full right-4 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-200 translate-y-px"></div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </motion.div>
      </div>
    </div>
  );
}

export default function ThanksPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <ThanksPageContent />
    </Suspense>
  );
}
